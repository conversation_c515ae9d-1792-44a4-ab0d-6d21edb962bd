name: Run golangci-lint

on:
  pull_request:
    branches: [main]

jobs:
  golangci-lint:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5

      - name: golangci-lint
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9 # v8
        with:
          version: v2.1.0
          only-new-issues: true