/*
Copyright 2023 The K8sGPT Authors.
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package ai

import (
	"context"
)

const noopAIClientName = "noopai"

type NoOpAIClient struct {
	nopCloser
}

func (c *NoOpAIClient) Configure(_ IAIConfig) error {
	return nil
}

func (c *NoOpAIClient) GetCompletion(_ context.Context, prompt string) (string, error) {
	response := "I am a noop response to the prompt " + prompt
	return response, nil
}

func (c *NoOpAIClient) GetName() string {
	return noopAIClientName
}
