{"packages": {".": {"changelog-path": "CHANGELOG.md", "release-type": "go", "prerelease": false, "bump-minor-pre-major": true, "bump-patch-for-minor-pre-major": true, "draft": false, "extra-files": ["README.md", "deploy/manifest.yaml", "chart/Chart.yaml", "chart/values.yaml", "container/manifests/deployment.yaml"], "changelog-sections": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Other"}, {"type": "docs", "section": "Docs"}, {"type": "perf", "section": "Performance"}, {"type": "build", "hidden": true, "section": "Build"}, {"type": "deps", "section": "Dependency Updates"}, {"type": "ci", "hidden": true, "section": "CI"}, {"type": "refactor", "section": "Refactoring"}, {"type": "revert", "hidden": true, "section": "Reverts"}, {"type": "style", "hidden": true, "section": "Styl<PERSON>"}, {"type": "test", "hidden": true, "section": "Tests"}]}}, "$schema": "https://raw.githubusercontent.com/googleapis/release-please/main/schemas/config.json"}