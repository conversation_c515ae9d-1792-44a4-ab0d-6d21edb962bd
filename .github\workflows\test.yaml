name: Run tests

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  GO_VERSION: "~1.22"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5

      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Run test
        run: go test ./... -coverprofile=coverage.txt
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
