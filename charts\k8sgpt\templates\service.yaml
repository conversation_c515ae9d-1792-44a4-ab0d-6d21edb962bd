apiVersion: v1
kind: Service
metadata:
  name: {{ template "k8sgpt.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    {{- include "k8sgpt.labels" . | nindent 4 }}
  {{- if .Values.service.annotations }}
  annotations:
  {{- toYaml .Values.service.annotations | nindent 4 }}
  {{- end }}
spec:
  selector:
    app.kubernetes.io/name: {{ include "k8sgpt.name" . }}
  ports:
    - name: http
      port: 8080
      targetPort: 8080
    - name: metrics
      port: 8081
      targetPort: 8081
    {{- if .Values.deployment.mcp.enabled }}
    - name: mcp
      port: {{ .Values.deployment.mcp.port | int }}
      targetPort: {{ .Values.deployment.mcp.port | int }}
    {{- end }}
  type: {{ .Values.service.type }}
